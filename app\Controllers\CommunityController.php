<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\CommunityPostModel;
use App\Models\CommunityLikeModel;
use App\Models\CommunityCommentModel;
use App\Models\CommunityFollowModel;
use App\Models\UserBadgeModel;
use App\Models\UserModel;

class CommunityController extends BaseController
{
    protected $postModel;
    protected $likeModel;
    protected $commentModel;
    protected $followModel;
    protected $badgeModel;
    protected $userModel;

    public function __construct()
    {
        $this->postModel = new CommunityPostModel();
        $this->likeModel = new CommunityLikeModel();
        $this->commentModel = new CommunityCommentModel();
        $this->followModel = new CommunityFollowModel();
        $this->badgeModel = new UserBadgeModel();
        $this->userModel = new UserModel();
    }

    /**
     * Display community page
     */
    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $data['title'] = 'Community';
        $data['active'] = 'community';
        $data['userDetails'] = $this->userModel->find($userId);
        $data['customScript'] = 'community';
        $data['main_content'] = 'pages/community';

        return view('includes/template', $data);
    }

    /**
     * Get posts for the feed
     */
    public function getPosts()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        // Get filters from request
        $filters = [
            'post_type' => $this->request->getGet('post_type') ?? 'all',
            'asset_type' => $this->request->getGet('asset_type') ?? 'all',
            'sort' => $this->request->getGet('sort') ?? 'latest',
            'user_id' => $this->request->getGet('user_id') ?? null
        ];

        $page = (int)($this->request->getGet('page') ?? 1);
        $limit = 10;
        $offset = ($page - 1) * $limit;

        try {
            // Get posts with user information
            $posts = $this->postModel->getPostsWithUserInfo($filters, $limit, $offset);
            
            // Enhance posts with additional data
            foreach ($posts as &$post) {
                // Get user badges
                $post['badges'] = $this->badgeModel->getUserBadges($post['user_id']);
                
                // Check if current user has liked this post
                $post['user_has_liked'] = $this->likeModel->hasUserLikedPost($userId, $post['id']);
                
                // Check if current user is following the post author
                $post['user_is_following'] = $this->followModel->isFollowing($userId, $post['user_id']);
                
                // Format timestamps
                $post['time_ago'] = $this->timeAgo($post['created_at']);
                
                // Parse hashtags, mentions, and stock tags
                $post['hashtags_array'] = $post['hashtags'] ? explode(',', $post['hashtags']) : [];
                $post['mentions_array'] = $post['mentions'] ? explode(',', $post['mentions']) : [];
                $post['stock_tags_array'] = $post['stock_tags'] ? explode(',', $post['stock_tags']) : [];
            }

            return $this->response->setJSON([
                'success' => true,
                'posts' => $posts,
                'has_more' => count($posts) === $limit
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error fetching posts: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create a new post
     */
    public function createPost()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $validation = \Config\Services::validation();
        $validation->setRules([
            'title' => 'required|max_length[255]',
            'content' => 'required',
            'post_type' => 'required|in_list[setup_alert,pnl_share,educational,analysis,journal]',
            'asset_type' => 'required|in_list[all,equity,options,futures,crypto,forex]',
            'hashtags' => 'permit_empty|max_length[500]',
            'mentions' => 'permit_empty|max_length[500]',
            'stock_tags' => 'permit_empty|max_length[500]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        try {
            $postData = [
                'user_id' => $userId,
                'title' => $this->request->getPost('title'),
                'content' => $this->request->getPost('content'),
                'post_type' => $this->request->getPost('post_type'),
                'asset_type' => $this->request->getPost('asset_type'),
                'hashtags' => $this->request->getPost('hashtags'),
                'mentions' => $this->request->getPost('mentions'),
                'stock_tags' => $this->request->getPost('stock_tags')
            ];

            // Handle image upload
            $image = $this->request->getFile('image');
            if ($image && $image->isValid() && !$image->hasMoved()) {
                $newName = $image->getRandomName();
                $image->move(WRITEPATH . 'uploads/community', $newName);
                $postData['image_path'] = 'uploads/community/' . $newName;
            }

            $postId = $this->postModel->insert($postData);

            if ($postId) {
                // Auto-assign badges based on activity
                $this->badgeModel->autoAssignBadges($userId);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Post created successfully',
                    'post_id' => $postId
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create post'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error creating post: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Toggle like on a post
     */
    public function toggleLike()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $postId = $this->request->getPost('post_id');

        if (!$postId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Post ID is required'
            ]);
        }

        try {
            $isLiked = $this->likeModel->toggleLike($userId, $postId);
            
            // Update post like count
            if ($isLiked) {
                $this->postModel->incrementLikeCount($postId);
            } else {
                $this->postModel->decrementLikeCount($postId);
            }

            // Get updated like count
            $likeCount = $this->likeModel->getLikeCount($postId);

            return $this->response->setJSON([
                'success' => true,
                'is_liked' => $isLiked,
                'like_count' => $likeCount
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error toggling like: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Add a comment to a post
     */
    public function addComment()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $validation = \Config\Services::validation();
        $validation->setRules([
            'post_id' => 'required|integer',
            'content' => 'required|max_length[1000]',
            'parent_comment_id' => 'permit_empty|integer'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        try {
            $commentData = [
                'user_id' => $userId,
                'post_id' => $this->request->getPost('post_id'),
                'content' => $this->request->getPost('content'),
                'parent_comment_id' => $this->request->getPost('parent_comment_id') ?: null
            ];

            $commentId = $this->commentModel->addComment($commentData);

            if ($commentId) {
                // Get the comment with user info
                $comment = $this->commentModel->getCommentWithUserInfo($commentId);
                $comment['time_ago'] = $this->timeAgo($comment['created_at']);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Comment added successfully',
                    'comment' => $comment
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to add comment'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error adding comment: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get comments for a post
     */
    public function getComments()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $postId = $this->request->getGet('post_id');

        if (!$postId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Post ID is required'
            ]);
        }

        try {
            $comments = $this->commentModel->getPostComments($postId);
            
            // Add time ago for each comment and its replies
            $this->addTimeAgoToComments($comments);

            return $this->response->setJSON([
                'success' => true,
                'comments' => $comments
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error fetching comments: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Helper function to add time_ago to comments recursively
     */
    private function addTimeAgoToComments(&$comments)
    {
        foreach ($comments as &$comment) {
            $comment['time_ago'] = $this->timeAgo($comment['created_at']);
            if (!empty($comment['replies'])) {
                $this->addTimeAgoToComments($comment['replies']);
            }
        }
    }

    /**
     * Toggle follow relationship
     */
    public function toggleFollow()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $followingId = $this->request->getPost('following_id');

        if (!$followingId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Following ID is required'
            ]);
        }

        if ($userId == $followingId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You cannot follow yourself'
            ]);
        }

        try {
            $isFollowing = $this->followModel->toggleFollow($userId, $followingId);

            // Get updated follower count
            $followerCount = $this->followModel->getFollowerCount($followingId);

            return $this->response->setJSON([
                'success' => true,
                'is_following' => $isFollowing,
                'follower_count' => $followerCount
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error toggling follow: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get user's own posts
     */
    public function getMyPosts()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $filters = [
            'user_id' => $userId,
            'post_type' => $this->request->getGet('post_type') ?? 'all',
            'asset_type' => $this->request->getGet('asset_type') ?? 'all',
            'sort' => $this->request->getGet('sort') ?? 'latest'
        ];

        $page = (int)($this->request->getGet('page') ?? 1);
        $limit = 10;
        $offset = ($page - 1) * $limit;

        try {
            $posts = $this->postModel->getPostsWithUserInfo($filters, $limit, $offset);

            // Enhance posts with additional data
            foreach ($posts as &$post) {
                $post['badges'] = $this->badgeModel->getUserBadges($post['user_id']);
                $post['user_has_liked'] = $this->likeModel->hasUserLikedPost($userId, $post['id']);
                $post['user_is_following'] = false; // User can't follow themselves
                $post['time_ago'] = $this->timeAgo($post['created_at']);
                $post['hashtags_array'] = $post['hashtags'] ? explode(',', $post['hashtags']) : [];
                $post['mentions_array'] = $post['mentions'] ? explode(',', $post['mentions']) : [];
                $post['stock_tags_array'] = $post['stock_tags'] ? explode(',', $post['stock_tags']) : [];
            }

            return $this->response->setJSON([
                'success' => true,
                'posts' => $posts,
                'has_more' => count($posts) === $limit
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error fetching posts: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Upload image for post
     */
    public function uploadImage()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $image = $this->request->getFile('image');

        if (!$image || !$image->isValid()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No valid image uploaded'
            ]);
        }

        // Validate image type and size
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!in_array($image->getMimeType(), $allowedTypes)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid image type. Only JPEG, PNG, and GIF are allowed.'
            ]);
        }

        if ($image->getSize() > 5 * 1024 * 1024) { // 5MB limit
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Image size must be less than 5MB'
            ]);
        }

        try {
            $newName = $image->getRandomName();
            $uploadPath = WRITEPATH . 'uploads/community/';

            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $image->move($uploadPath, $newName);

            return $this->response->setJSON([
                'success' => true,
                'image_path' => 'uploads/community/' . $newName,
                'image_url' => base_url('writable/uploads/community/' . $newName)
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error uploading image: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Helper function to calculate time ago
     */
    private function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . 'm ago';
        if ($time < 86400) return floor($time/3600) . 'h ago';
        if ($time < 2592000) return floor($time/86400) . 'd ago';
        if ($time < 31536000) return floor($time/2592000) . 'mo ago';
        return floor($time/31536000) . 'y ago';
    }
}
