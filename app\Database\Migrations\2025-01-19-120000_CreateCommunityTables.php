<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateCommunityTables extends Migration
{
    public function up()
    {
        // Create community_posts table
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'title' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
            ],
            'content' => [
                'type' => 'TEXT',
            ],
            'post_type' => [
                'type'       => 'ENUM',
                'constraint' => ['setup_alert', 'pnl_share', 'educational', 'analysis', 'journal'],
                'default'    => 'setup_alert',
            ],
            'asset_type' => [
                'type'       => 'ENUM',
                'constraint' => ['all', 'equity', 'options', 'futures', 'crypto', 'forex'],
                'default'    => 'all',
            ],
            'image_path' => [
                'type'       => 'VARCHAR',
                'constraint' => 500,
                'null'       => true,
            ],
            'hashtags' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'mentions' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'stock_tags' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'like_count' => [
                'type'    => 'INT',
                'default' => 0,
            ],
            'comment_count' => [
                'type'    => 'INT',
                'default' => 0,
            ],
            'share_count' => [
                'type'    => 'INT',
                'default' => 0,
            ],
            'is_featured' => [
                'type'    => 'BOOLEAN',
                'default' => false,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('post_type');
        $this->forge->addKey('asset_type');
        $this->forge->addKey('created_at');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('community_posts');

        // Create community_likes table
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'post_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['user_id', 'post_id'], false, true); // Unique constraint
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('post_id', 'community_posts', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('community_likes');

        // Create community_comments table
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'post_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'parent_comment_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true,
            ],
            'content' => [
                'type' => 'TEXT',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('post_id');
        $this->forge->addKey('parent_comment_id');
        $this->forge->addKey('created_at');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('post_id', 'community_posts', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('parent_comment_id', 'community_comments', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('community_comments');

        // Create community_follows table
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'follower_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'following_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['follower_id', 'following_id'], false, true); // Unique constraint
        $this->forge->addForeignKey('follower_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('following_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('community_follows');

        // Create user_badges table
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'badge_type' => [
                'type'       => 'ENUM',
                'constraint' => ['pro_analyst', 'consistent_gainer', 'chart_master', 'pro_member', 'verified'],
                'default'    => 'pro_member',
            ],
            'badge_name' => [
                'type'       => 'VARCHAR',
                'constraint' => 100,
            ],
            'badge_color' => [
                'type'       => 'VARCHAR',
                'constraint' => 50,
                'default'    => 'blue',
            ],
            'is_active' => [
                'type'    => 'BOOLEAN',
                'default' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('badge_type');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('user_badges');
    }

    public function down()
    {
        $this->forge->dropTable('user_badges');
        $this->forge->dropTable('community_follows');
        $this->forge->dropTable('community_comments');
        $this->forge->dropTable('community_likes');
        $this->forge->dropTable('community_posts');
    }
}
