# Community Panel Setup Guide

This guide explains how to set up and use the fully functional community panel for your Trade Diary application.

## Features Implemented

### ✅ Core Functionality
- **Post Creation & Data Persistence**: Users can create posts with different types (Setup Alert, P&L Share, Educational, Analysis, Journal)
- **Like System**: Real-time like/unlike functionality with persistent storage
- **Comment System**: Nested comments with reply functionality
- **Follow/Unfollow System**: Users can follow/unfollow each other
- **Filtering & Sorting**: Filter by asset type, post type, and sort by various criteria
- **My Posts Feature**: Users can view their own posts separately

### ✅ Technical Implementation
- **Backend**: CodeIgniter 4 controllers, models, and database structure
- **Frontend**: JavaScript for interactive features
- **Database**: Proper relational database design with foreign keys
- **Responsive Design**: Mobile-friendly interface preserved from original design

## Installation Steps

### 1. Database Setup

Run the migration to create the required tables:
```bash
php spark migrate
```

This will create the following tables:
- `community_posts` - Stores all community posts
- `community_likes` - Stores like relationships
- `community_comments` - Stores comments and replies
- `community_follows` - Stores follow relationships
- `user_badges` - Stores user badges and achievements

### 2. Sample Data (Optional)

Add sample data to test the functionality:
```bash
php spark db:seed CommunitySeeder
```

### 3. File Structure

The following files have been created/modified:

**Database:**
- `app/Database/Migrations/2025-01-19-120000_CreateCommunityTables.php`
- `app/Database/Seeds/CommunitySeeder.php`

**Models:**
- `app/Models/CommunityPostModel.php`
- `app/Models/CommunityLikeModel.php`
- `app/Models/CommunityCommentModel.php`
- `app/Models/CommunityFollowModel.php`
- `app/Models/UserBadgeModel.php`

**Controllers:**
- `app/Controllers/CommunityController.php`

**Views:**
- `app/Views/pages/community.php`

**Assets:**
- `assets/js/community.js`
- `assets/css/community.css`

**Routes:**
- Updated `app/Config/Routes.php` with community routes

### 4. Testing

Run the test script to verify everything is working:
```bash
php test_community.php
```

## Usage

### Accessing the Community

Navigate to: `http://your-domain/Community`

### API Endpoints

The following API endpoints are available:

- `GET /community/posts` - Get posts with filtering and pagination
- `POST /community/create-post` - Create a new post
- `POST /community/toggle-like` - Like/unlike a post
- `POST /community/add-comment` - Add a comment or reply
- `GET /community/comments` - Get comments for a post
- `POST /community/toggle-follow` - Follow/unfollow a user
- `GET /community/my-posts` - Get current user's posts
- `POST /community/upload-image` - Upload images for posts

### Frontend Features

**Navigation:**
- Home Feed: Shows all community posts
- My Posts: Shows only current user's posts
- Sidebar navigation with collapsible design

**Post Types:**
- Setup Alert: Trading setup ideas
- P&L Share: Profit/loss sharing
- Educational: Educational content
- Analysis: Market analysis
- Journal: Trading journal entries

**Filtering:**
- Asset Type: All, Equity, Options, Futures, Crypto, Forex
- Sort: Latest, Trending, Top This Week, Most Liked, Educational
- Tabs: All, Setups, P&L, Analysis, Journals

**Interactive Features:**
- Like/unlike posts with real-time count updates
- Comment on posts with nested replies
- Follow/unfollow users
- Image upload for charts and screenshots
- Hashtags, mentions, and stock tags support

## Database Schema

### community_posts
- `id` - Primary key
- `user_id` - Foreign key to users table
- `title` - Post title
- `content` - Post content
- `post_type` - Type of post (enum)
- `asset_type` - Asset category (enum)
- `image_path` - Path to uploaded image
- `hashtags` - Comma-separated hashtags
- `mentions` - Comma-separated user mentions
- `stock_tags` - Comma-separated stock symbols
- `like_count` - Number of likes
- `comment_count` - Number of comments
- `share_count` - Number of shares
- `is_featured` - Featured post flag
- Timestamps and soft deletes

### community_likes
- `id` - Primary key
- `user_id` - Foreign key to users table
- `post_id` - Foreign key to community_posts table
- `created_at` - Timestamp
- Unique constraint on (user_id, post_id)

### community_comments
- `id` - Primary key
- `user_id` - Foreign key to users table
- `post_id` - Foreign key to community_posts table
- `parent_comment_id` - Self-referencing foreign key for replies
- `content` - Comment content
- Timestamps and soft deletes

### community_follows
- `id` - Primary key
- `follower_id` - Foreign key to users table (who follows)
- `following_id` - Foreign key to users table (who is followed)
- `created_at` - Timestamp
- Unique constraint on (follower_id, following_id)

### user_badges
- `id` - Primary key
- `user_id` - Foreign key to users table
- `badge_type` - Type of badge (enum)
- `badge_name` - Display name of badge
- `badge_color` - Badge color
- `is_active` - Active status
- Timestamps

## Customization

### Adding New Post Types
1. Update the enum in the migration file
2. Add the new type to the validation rules in the model
3. Update the frontend JavaScript and HTML

### Adding New Badge Types
1. Update the enum in the user_badges table
2. Modify the UserBadgeModel to handle the new type
3. Update the auto-assignment logic if needed

### Styling
- Modify `assets/css/community.css` for visual changes
- The design preserves the original dark theme with teal accents
- Responsive design is maintained for mobile devices

## Security Considerations

- All user inputs are validated and sanitized
- CSRF protection should be enabled in CodeIgniter
- File uploads are restricted to image types only
- SQL injection protection through CodeIgniter's query builder
- User authentication is required for all actions

## Performance Optimization

- Database indexes on frequently queried columns
- Pagination for large datasets
- Lazy loading of comments
- Image optimization recommended for uploads
- Caching can be implemented for popular posts

## Troubleshooting

1. **Tables not found**: Run `php spark migrate`
2. **No posts showing**: Check if sample data exists or create some posts
3. **JavaScript errors**: Ensure all files are properly included
4. **Permission errors**: Check file permissions for upload directory
5. **Database connection**: Verify database configuration in `.env`

## Future Enhancements

Potential features that can be added:
- Real-time notifications
- Post scheduling
- Advanced search functionality
- User reputation system
- Content moderation tools
- Export functionality
- Mobile app API
- Social media integration
