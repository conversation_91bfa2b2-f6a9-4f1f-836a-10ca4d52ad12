<?php

namespace App\Models;

use CodeIgniter\Model;

class UserBadgeModel extends Model
{
    protected $table = 'user_badges';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $allowedFields = [
        'user_id',
        'badge_type',
        'badge_name',
        'badge_color',
        'is_active'
    ];

    protected $validationRules = [
        'user_id' => 'required|integer',
        'badge_type' => 'required|in_list[pro_analyst,consistent_gainer,chart_master,pro_member,verified]',
        'badge_name' => 'required|max_length[100]',
        'badge_color' => 'required|max_length[50]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'badge_type' => [
            'required' => 'Badge type is required',
            'in_list' => 'Invalid badge type'
        ],
        'badge_name' => [
            'required' => 'Badge name is required',
            'max_length' => 'Badge name cannot exceed 100 characters'
        ],
        'badge_color' => [
            'required' => 'Badge color is required',
            'max_length' => 'Badge color cannot exceed 50 characters'
        ]
    ];

    /**
     * Get active badges for a user
     */
    public function getUserBadges($userId)
    {
        return $this->where('user_id', $userId)
                   ->where('is_active', true)
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }

    /**
     * Get all badges for a user (including inactive)
     */
    public function getAllUserBadges($userId)
    {
        return $this->where('user_id', $userId)
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }

    /**
     * Add a badge to a user
     */
    public function addBadgeToUser($userId, $badgeType, $badgeName, $badgeColor = 'blue')
    {
        // Check if user already has this badge type
        $existingBadge = $this->where('user_id', $userId)
                             ->where('badge_type', $badgeType)
                             ->first();

        if ($existingBadge) {
            // Update existing badge
            return $this->update($existingBadge['id'], [
                'badge_name' => $badgeName,
                'badge_color' => $badgeColor,
                'is_active' => true
            ]);
        } else {
            // Create new badge
            return $this->insert([
                'user_id' => $userId,
                'badge_type' => $badgeType,
                'badge_name' => $badgeName,
                'badge_color' => $badgeColor,
                'is_active' => true
            ]);
        }
    }

    /**
     * Remove a badge from a user
     */
    public function removeBadgeFromUser($userId, $badgeType)
    {
        return $this->where('user_id', $userId)
                   ->where('badge_type', $badgeType)
                   ->set('is_active', false)
                   ->update();
    }

    /**
     * Get users with a specific badge type
     */
    public function getUsersWithBadge($badgeType, $limit = 50)
    {
        $builder = $this->db->table($this->table . ' b');
        $builder->select('u.id, u.full_name, u.profile, b.badge_name, b.badge_color');
        $builder->join('users u', 'u.id = b.user_id');
        $builder->where('b.badge_type', $badgeType);
        $builder->where('b.is_active', true);
        $builder->where('u.deleted_at IS NULL');
        $builder->orderBy('b.created_at', 'ASC');
        $builder->limit($limit);
        return $builder->get()->getResultArray();
    }

    /**
     * Get badge statistics
     */
    public function getBadgeStats()
    {
        $builder = $this->db->table($this->table);
        $builder->select('badge_type, COUNT(*) as count');
        $builder->where('is_active', true);
        $builder->groupBy('badge_type');
        return $builder->get()->getResultArray();
    }

    /**
     * Auto-assign badges based on user activity
     */
    public function autoAssignBadges($userId)
    {
        $db = \Config\Database::connect();
        
        // Get user's trading statistics
        $userStats = $db->table('trades')
                       ->select('COUNT(*) as total_trades, 
                                SUM(CASE WHEN pnl_amount > 0 THEN 1 ELSE 0 END) as winning_trades,
                                AVG(pnl_amount) as avg_pnl')
                       ->where('user_id', $userId)
                       ->where('deleted_at IS NULL')
                       ->get()
                       ->getRowArray();

        if ($userStats && $userStats['total_trades'] > 0) {
            $winRate = ($userStats['winning_trades'] / $userStats['total_trades']) * 100;
            
            // Assign badges based on performance
            if ($userStats['total_trades'] >= 100 && $winRate >= 70) {
                $this->addBadgeToUser($userId, 'consistent_gainer', 'Consistent Gainer', 'green');
            }
            
            if ($userStats['total_trades'] >= 50 && $userStats['avg_pnl'] > 1000) {
                $this->addBadgeToUser($userId, 'pro_analyst', 'Pro Analyst', 'purple');
            }
        }

        // Get user's community activity
        $postCount = $db->table('community_posts')
                       ->where('user_id', $userId)
                       ->where('deleted_at IS NULL')
                       ->countAllResults();

        if ($postCount >= 10) {
            $this->addBadgeToUser($userId, 'chart_master', 'Chart Master', 'blue');
        }

        // Check if user has active subscription
        $user = $db->table('users')
                  ->where('id', $userId)
                  ->get()
                  ->getRowArray();

        if ($user && $user['sub_end'] && strtotime($user['sub_end']) > time()) {
            $this->addBadgeToUser($userId, 'pro_member', 'Pro Member', 'gold');
        }
    }

    /**
     * Get badge color class for CSS
     */
    public function getBadgeColorClass($badgeType)
    {
        $colorMap = [
            'pro_analyst' => 'badge-pro',
            'consistent_gainer' => 'badge-gainer',
            'chart_master' => 'badge-master',
            'pro_member' => 'badge-pro',
            'verified' => 'badge-verified'
        ];

        return $colorMap[$badgeType] ?? 'badge-default';
    }
}
