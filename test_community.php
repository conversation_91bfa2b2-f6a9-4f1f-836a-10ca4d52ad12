<?php
/**
 * Simple test script to verify community functionality
 * Run this from the command line: php test_community.php
 */

// Include CodeIgniter bootstrap
require_once 'app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Get database instance
$db = \Config\Database::connect();

echo "=== Community Functionality Test ===\n\n";

// Test 1: Check if tables exist
echo "1. Checking if community tables exist...\n";
$tables = ['community_posts', 'community_likes', 'community_comments', 'community_follows', 'user_badges'];
$allTablesExist = true;

foreach ($tables as $table) {
    if ($db->tableExists($table)) {
        echo "   ✓ Table '$table' exists\n";
    } else {
        echo "   ✗ Table '$table' does not exist\n";
        $allTablesExist = false;
    }
}

if (!$allTablesExist) {
    echo "\nSome tables are missing. Please run: php spark migrate\n";
    exit(1);
}

// Test 2: Check if we can insert sample data
echo "\n2. Testing data insertion...\n";

try {
    // Insert a test post
    $postData = [
        'user_id' => 1,
        'title' => 'Test Post',
        'content' => 'This is a test post to verify functionality.',
        'post_type' => 'setup_alert',
        'asset_type' => 'equity',
        'hashtags' => 'Test,Community',
        'mentions' => '',
        'stock_tags' => 'NIFTY',
        'like_count' => 0,
        'comment_count' => 0,
        'share_count' => 0,
        'is_featured' => false,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];

    $result = $db->table('community_posts')->insert($postData);
    if ($result) {
        $postId = $db->insertID();
        echo "   ✓ Successfully inserted test post (ID: $postId)\n";
        
        // Test like insertion
        $likeData = [
            'user_id' => 1,
            'post_id' => $postId,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $likeResult = $db->table('community_likes')->insert($likeData);
        if ($likeResult) {
            echo "   ✓ Successfully inserted test like\n";
        } else {
            echo "   ✗ Failed to insert test like\n";
        }
        
        // Test comment insertion
        $commentData = [
            'user_id' => 1,
            'post_id' => $postId,
            'parent_comment_id' => null,
            'content' => 'This is a test comment.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $commentResult = $db->table('community_comments')->insert($commentData);
        if ($commentResult) {
            echo "   ✓ Successfully inserted test comment\n";
        } else {
            echo "   ✗ Failed to insert test comment\n";
        }
        
        // Clean up test data
        $db->table('community_comments')->where('post_id', $postId)->delete();
        $db->table('community_likes')->where('post_id', $postId)->delete();
        $db->table('community_posts')->where('id', $postId)->delete();
        echo "   ✓ Cleaned up test data\n";
        
    } else {
        echo "   ✗ Failed to insert test post\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error during data insertion: " . $e->getMessage() . "\n";
}

// Test 3: Check if models can be loaded
echo "\n3. Testing model loading...\n";

try {
    $postModel = new \App\Models\CommunityPostModel();
    echo "   ✓ CommunityPostModel loaded successfully\n";
    
    $likeModel = new \App\Models\CommunityLikeModel();
    echo "   ✓ CommunityLikeModel loaded successfully\n";
    
    $commentModel = new \App\Models\CommunityCommentModel();
    echo "   ✓ CommunityCommentModel loaded successfully\n";
    
    $followModel = new \App\Models\CommunityFollowModel();
    echo "   ✓ CommunityFollowModel loaded successfully\n";
    
    $badgeModel = new \App\Models\UserBadgeModel();
    echo "   ✓ UserBadgeModel loaded successfully\n";
    
} catch (Exception $e) {
    echo "   ✗ Error loading models: " . $e->getMessage() . "\n";
}

// Test 4: Check if controller exists
echo "\n4. Testing controller...\n";

try {
    $controller = new \App\Controllers\CommunityController();
    echo "   ✓ CommunityController loaded successfully\n";
} catch (Exception $e) {
    echo "   ✗ Error loading controller: " . $e->getMessage() . "\n";
}

// Test 5: Check if routes are configured
echo "\n5. Checking routes configuration...\n";

$routesFile = file_get_contents('app/Config/Routes.php');
if (strpos($routesFile, 'CommunityController') !== false) {
    echo "   ✓ Community routes are configured\n";
} else {
    echo "   ✗ Community routes are not configured\n";
}

// Test 6: Check if JavaScript file exists
echo "\n6. Checking JavaScript file...\n";

if (file_exists('assets/js/community.js')) {
    echo "   ✓ Community JavaScript file exists\n";
} else {
    echo "   ✗ Community JavaScript file does not exist\n";
}

// Test 7: Check if view file exists
echo "\n7. Checking view file...\n";

if (file_exists('app/Views/pages/community.php')) {
    echo "   ✓ Community view file exists\n";
} else {
    echo "   ✗ Community view file does not exist\n";
}

echo "\n=== Test Complete ===\n";
echo "\nNext steps:\n";
echo "1. Run 'php spark migrate' to create the database tables\n";
echo "2. Run 'php spark db:seed CommunitySeeder' to add sample data\n";
echo "3. Access the community page at: http://your-domain/Community\n";
echo "4. Test the functionality in your browser\n";
?>
