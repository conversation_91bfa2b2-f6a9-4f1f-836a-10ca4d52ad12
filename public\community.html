<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trade Diary | Trading Community</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        // Set base URL for JavaScript
        window.base_url = window.location.origin + '/';
    </script>
    <style>
        :root {
            --primary: #4fd1c5;
            --primary-dark: #319795;
            --secondary: #805ad5;
            --secondary-dark: #6b46c1;
            --accent: #f687b3;
            --accent-dark: #e53e3e;
            --dark: #1a202c;
            --darker: #171923;
            --light: #f7fafc;
            --gray: #e2e8f0;
            --dark-gray: #2d3748;
        }

        body {
            font-family: 'Space Grotesk', sans-serif;
            background-color: var(--darker);
            color: var(--gray);
            transition: all 0.3s ease;
        }

        .glass-card {
            background: rgba(26, 32, 44, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .sidebar {
            transition: all 0.3s ease;
            background-color: var(--dark);
        }

        .sidebar-collapsed {
            width: 80px !important;
        }

        .sidebar-collapsed .nav-text {
            display: none;
        }

        .sidebar-collapsed .logo-text {
            display: none;
        }

        .sidebar-collapsed .logo-icon {
            margin: 0 auto;
        }

        .post-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(79, 209, 197, 0.1);
        }

        .glow-button {
            box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
            }

            50% {
                box-shadow: 0 0 25px rgba(79, 209, 197, 0.8);
            }

            100% {
                box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
            }
        }

        .hashtag {
            color: var(--primary);
        }

        .mention {
            color: var(--accent);
        }

        .stock-tag {
            color: var(--secondary);
        }

        .code-block {
            background-color: #2d3748;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        .reaction-btn:hover {
            transform: scale(1.1);
        }

        .badge-pro {
            background: linear-gradient(90deg, var(--primary), var(--secondary));
        }

        .badge-gainer {
            background: linear-gradient(90deg, #48bb78, #38b2ac);
        }

        .badge-master {
            background: linear-gradient(90deg, var(--accent), #ed8936);
        }

        .tab-active {
            border-bottom: 2px solid var(--primary);
            color: var(--primary);
        }

        .emoji-picker {
            position: absolute;
            bottom: 50px;
            right: 0;
            z-index: 10;
        }

        .comment-section {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .comment-section.active {
            max-height: 1000px;
            transition: max-height 0.5s ease-in;
        }

        .comment-input {
            background: rgba(45, 55, 72, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .comment-input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .neon-text {
            text-shadow: 0 0 5px rgba(79, 209, 197, 0.5);
        }

        .modal {
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .modal.active .modal-content {
            transform: translateY(0);
        }

        .post-type-btn {
            transition: all 0.2s ease;
        }

        .post-type-btn.active {
            background-color: var(--primary);
            color: white;
        }

        .post-type-btn:hover:not(.active) {
            background-color: rgba(79, 209, 197, 0.1);
        }

        .follow-btn {
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            transition: all 0.3s ease;
        }

        .follow-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(79, 209, 197, 0.3);
        }

        .follow-btn.following {
            background: var(--dark-gray);
            border: 1px solid var(--primary);
        }

        .reply-section {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            margin-left: 2rem;
            border-left: 2px solid var(--primary);
            padding-left: 1rem;
        }

        .reply-section.active {
            max-height: 200px;
        }

        .reply-btn {
            color: var(--primary);
            font-size: 0.8rem;
        }

        .reply-btn:hover {
            text-decoration: underline;
        }

        .reply-indicator {
            color: var(--primary);
            font-size: 0.7rem;
            margin-left: 0.5rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -300px;
                z-index: 100;
                width: 300px;
                height: 100vh;
            }

            .sidebar.active {
                left: 0;
            }

            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.7);
                z-index: 90;
                display: none;
            }

            .overlay.active {
                display: block;
            }

            .feed-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .feed-header h1 {
                margin-bottom: 1rem;
            }

            .feed-filters {
                width: 100%;
                flex-direction: column;
                gap: 0.5rem;
            }

            .feed-filters select {
                width: 100%;
            }

            .tabs {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 0.5rem;
                -webkit-overflow-scrolling: touch;
            }

            .tabs::-webkit-scrollbar {
                display: none;
            }

            .post-actions {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .post-actions>div {
                flex: 1 1 100%;
                justify-content: space-between;
            }

            .post-actions .emoji-picker-container {
                width: 100%;
                text-align: right;
            }

            .user-info {
                flex-direction: column;
                align-items: flex-start;
            }

            .user-badges {
                margin-top: 0.5rem;
                width: 100%;
                justify-content: space-between;
            }

            .modal-content {
                width: 95%;
                margin: 0 auto;
            }

            .post-type-buttons {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 0.5rem;
            }

            .post-type-buttons::-webkit-scrollbar {
                display: none;
            }

            .post-type-btn {
                min-width: 100px;
            }
        }

        @media (max-width: 480px) {
            .comment-input-container {
                flex-direction: column;
            }

            .comment-input-container input {
                border-radius: 0.5rem 0.5rem 0 0 !important;
                width: 100%;
            }

            .comment-input-container button {
                border-radius: 0 0 0.5rem 0.5rem !important;
                width: 100%;
            }

            .reply-section {
                margin-left: 1rem;
            }
        }
    </style>
</head>

<body class="min-h-screen">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div
            class="sidebar w-64 flex-shrink-0 flex flex-col border-r border-gray-800 transition-all duration-300 ease-in-out">
            <div class="flex items-center justify-between p-4 border-b border-gray-800">
                <div class="flex items-center space-x-2">
                    <div class="logo-icon">
                        <i class="fas fa-book text-2xl text-teal-400"></i>
                    </div>
                    <span class="logo-text text-xl font-bold text-teal-400 neon-text">TRADE DIARY</span>
                </div>
                <button id="sidebarToggle" class="text-gray-400 hover:text-white focus:outline-none">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>

            <div class="flex-1 overflow-y-auto py-4">
                <nav>
                    <ul class="space-y-2 px-2">
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-white bg-gray-800">
                                <i class="fas fa-home text-teal-400 w-6"></i>
                                <span class="nav-text ml-3">Home Feed</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-pen text-blue-400 w-6"></i>
                                <span class="nav-text ml-3">My Posts</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-lightbulb text-purple-400 w-6"></i>
                                <span class="nav-text ml-3">Setups & Ideas</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-chart-line text-green-400 w-6"></i>
                                <span class="nav-text ml-3">P&L Shares</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-search-dollar text-yellow-400 w-6"></i>
                                <span class="nav-text ml-3">Market Analysis</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-binoculars text-red-400 w-6"></i>
                                <span class="nav-text ml-3">Watchlist</span>
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-bell text-indigo-400 w-6"></i>
                                <span class="nav-text ml-3">Notifications</span>
                                <span
                                    class="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">3</span>
                            </a>
                        </li>
                    </ul>

                    <div class="border-t border-gray-800 mt-4 pt-4 px-2">
                        <ul class="space-y-2">
                            <li>
                                <a href="#"
                                    class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                    <i class="fas fa-user text-cyan-400 w-6"></i>
                                    <span class="nav-text ml-3">Profile</span>
                                </a>
                            </li>
                            <li>
                                <a href="#"
                                    class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                    <i class="fas fa-cog text-gray-400 w-6"></i>
                                    <span class="nav-text ml-3">Settings</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>

            <div class="p-4 border-t border-gray-800">
                <div class="flex items-center">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User"
                        class="w-10 h-10 rounded-full border-2 border-teal-400">
                    <div class="ml-3">
                        <div class="text-sm font-medium text-white">TraderX</div>
                        <div class="text-xs text-gray-400">Pro Member</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overlay for mobile -->
        <div class="overlay"></div>

        <!-- Main content -->
        <div class="flex-1 overflow-y-auto bg-gray-900">
            <!-- Mobile header -->
            <div class="lg:hidden flex items-center justify-between p-4 border-b border-gray-800">
                <button id="mobileSidebarToggle" class="text-gray-400 hover:text-white focus:outline-none">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <div class="text-xl font-bold text-teal-400 neon-text">TRADE DIARY</div>
                <div class="w-6"></div>
            </div>

            <!-- Main feed -->
            <div class="p-4">
                <!-- Feed header with filters -->
                <div class="flex items-center justify-between mb-6 feed-header">
                    <h1 class="text-2xl font-bold text-white neon-text">COMMUNITY FEED</h1>
                    <!-- Debug button -->
                    <button onclick="testConnection()" class="bg-red-600 text-white px-3 py-1 rounded text-sm">Test API</button>
                    <div class="flex space-x-2 feed-filters">
                        <div class="relative">
                            <select name="asset_type"
                                class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                                <option value="all" selected>All Assets</option>
                                <option value="equity">Equity</option>
                                <option value="options">Options</option>
                                <option value="futures">Futures</option>
                                <option value="crypto">Crypto</option>
                                <option value="forex">Forex</option>
                            </select>
                        </div>
                        <div class="relative">
                            <select name="sort"
                                class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                                <option value="latest" selected>Latest</option>
                                <option value="trending">Trending</option>
                                <option value="top_week">Top This Week</option>
                                <option value="most_liked">Most Liked</option>
                                <option value="educational">Educational</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="flex border-b border-gray-800 mb-6 tabs">
                    <button class="tab-button tab-active px-4 py-2 font-medium text-white">All</button>
                    <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Setups</button>
                    <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">P&L</button>
                    <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Analysis</button>
                    <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Journals</button>
                </div>

                <!-- Posts -->
                <div class="space-y-6" id="postsContainer">
                    <!-- Posts will be loaded dynamically via JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Add Post Modal -->
    <div class="modal fixed inset-0 z-50 flex items-center justify-center p-4">
        <div class="absolute inset-0 bg-black bg-opacity-70"></div>
        <div class="modal-content glass-card rounded-xl w-full max-w-2xl relative">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-white">Create New Post</h2>
                    <button class="text-gray-400 hover:text-white" onclick="toggleModal()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="postForm">
                    <!-- Post Type Selection -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-300 mb-2">Post Type</label>
                        <div class="flex flex-wrap gap-2 post-type-buttons">
                            <button type="button" class="post-type-btn active px-4 py-2 rounded-lg border border-gray-600 text-sm"
                                data-type="setup_alert">Setup Alert</button>
                            <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                                data-type="pnl_share">P&L Share</button>
                            <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                                data-type="educational">Educational</button>
                            <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                                data-type="analysis">Analysis</button>
                            <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                                data-type="journal">Journal</button>
                        </div>
                    </div>

                    <!-- Title -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                        <input type="text" id="postTitle" name="title" required
                            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500"
                            placeholder="Enter post title...">
                    </div>

                    <!-- Content -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-300 mb-2">Content</label>
                        <textarea id="postContent" name="content" required rows="4"
                            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500"
                            placeholder="Share your trading insights..."></textarea>
                    </div>

                    <!-- Image Upload -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-300 mb-2">Chart/Image (Optional)</label>
                        <input type="file" id="postImage" name="image" accept="image/*"
                            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500">
                    </div>

                    <!-- Tags -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-300 mb-2">Tags</label>
                        <input type="text" id="postTags" name="tags"
                            class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500"
                            placeholder="Add hashtags, mentions, stock symbols... (e.g., #BankNifty @TradeMaster $NIFTY)">
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="toggleModal()"
                            class="px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-800">
                            Cancel
                        </button>
                        <button type="submit"
                            class="px-4 py-2 bg-teal-600 hover:bg-teal-500 text-white rounded-lg glow-button">
                            Post
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button onclick="toggleModal()"
        class="fixed bottom-6 right-6 w-14 h-14 bg-teal-600 hover:bg-teal-500 text-white rounded-full shadow-lg glow-button z-40">
        <i class="fas fa-plus text-xl"></i>
    </button>

    <script>
    // Modal functionality
    function toggleModal() {
        const modal = document.querySelector('.modal');
        modal.classList.toggle('active');
    }

    // Post type selection
    document.addEventListener('click', function(e) {
        if (e.target.matches('.post-type-btn')) {
            document.querySelectorAll('.post-type-btn').forEach(btn => btn.classList.remove('active'));
            e.target.classList.add('active');
        }
    });

    // Test function
    function testConnection() {
        console.log('Testing API connection...');
        fetch(window.base_url + 'community/posts')
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                alert('API Response: ' + JSON.stringify(data));
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error: ' + error.message);
            });
    }
    </script>

    <!-- Include Community JavaScript -->
    <script src="./assets/js/community.js"></script>
</body>

</html>
