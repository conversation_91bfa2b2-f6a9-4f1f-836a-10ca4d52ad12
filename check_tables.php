<?php
$db = new mysqli('localhost', 'root', '', 'diary');
if ($db->connect_error) {
    die('Connection failed: ' . $db->connect_error);
}

$tables = ['community_posts', 'community_likes', 'community_comments', 'community_follows', 'user_badges'];
foreach ($tables as $table) {
    $result = $db->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "Table $table exists\n";
    } else {
        echo "Table $table does not exist\n";
    }
}

// Also check if contact_messages exists
$result = $db->query("SHOW TABLES LIKE 'contact_messages'");
if ($result->num_rows > 0) {
    echo "Table contact_messages exists\n";
} else {
    echo "Table contact_messages does not exist\n";
}

$db->close();
?>
