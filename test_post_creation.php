<?php
// Simple test to check if post creation endpoint works
echo "Testing Community Post Creation...\n\n";

// Test data
$postData = [
    'title' => 'Test Post from PHP',
    'content' => 'This is a test post to verify the API endpoint works.',
    'post_type' => 'setup_alert',
    'asset_type' => 'equity',
    'hashtags' => 'Test,API',
    'mentions' => '',
    'stock_tags' => 'NIFTY'
];

// Convert to URL-encoded format
$postString = http_build_query($postData);

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/community/create-post');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postString);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'Content-Length: ' . strlen($postString)
]);

// Execute request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}

echo "Response: $response\n\n";

// Test if we can access the posts endpoint
echo "Testing Posts Retrieval...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/community/posts');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}

echo "Response: $response\n";

// Check if the response is JSON
if ($response) {
    $data = json_decode($response, true);
    if ($data) {
        echo "JSON Response parsed successfully\n";
        if (isset($data['success'])) {
            echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        }
        if (isset($data['message'])) {
            echo "Message: " . $data['message'] . "\n";
        }
    } else {
        echo "Response is not valid JSON\n";
    }
}
?>
