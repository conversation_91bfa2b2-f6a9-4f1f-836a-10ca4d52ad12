<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\CommunityPostModel;

class CommunityDebugController extends BaseController
{
    public function test()
    {
        // Simple test endpoint without authentication
        try {
            $postModel = new CommunityPostModel();
            $posts = $postModel->findAll(5); // Get first 5 posts
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Community system is working',
                'post_count' => count($posts),
                'posts' => $posts
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }
    
    public function info()
    {
        // Show system information
        $info = [
            'php_version' => PHP_VERSION,
            'codeigniter_version' => \CodeIgniter\CodeIgniter::CI_VERSION,
            'base_url' => base_url(),
            'current_url' => current_url(),
            'environment' => ENVIRONMENT,
            'database_connected' => false,
            'tables_exist' => []
        ];
        
        try {
            $db = \Config\Database::connect();
            $info['database_connected'] = true;
            
            $tables = ['community_posts', 'community_likes', 'community_comments', 'community_follows', 'user_badges'];
            foreach ($tables as $table) {
                $info['tables_exist'][$table] = $db->tableExists($table);
            }
        } catch (\Exception $e) {
            $info['database_error'] = $e->getMessage();
        }
        
        return $this->response->setJSON($info);
    }

    public function testPost()
    {
        // Test post creation without authentication (for debugging only)
        try {
            $postModel = new CommunityPostModel();

            $testData = [
                'user_id' => 1, // Assuming user ID 1 exists
                'title' => 'Debug Test Post - ' . date('Y-m-d H:i:s'),
                'content' => 'This is a test post created via the debug endpoint.',
                'post_type' => 'setup_alert',
                'asset_type' => 'equity',
                'hashtags' => 'Debug,Test',
                'mentions' => '',
                'stock_tags' => 'NIFTY',
                'like_count' => 0,
                'comment_count' => 0,
                'share_count' => 0,
                'is_featured' => false
            ];

            $postId = $postModel->insert($testData);

            if ($postId) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Test post created successfully',
                    'post_id' => $postId
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create test post',
                    'errors' => $postModel->errors()
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }
}
