<?php

namespace App\Models;

use CodeIgniter\Model;

class CommunityPostModel extends Model
{
    protected $table = 'community_posts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $allowedFields = [
        'user_id',
        'title',
        'content',
        'post_type',
        'asset_type',
        'image_path',
        'hashtags',
        'mentions',
        'stock_tags',
        'like_count',
        'comment_count',
        'share_count',
        'is_featured'
    ];

    protected $validationRules = [
        'user_id' => 'required|integer',
        'title' => 'required|max_length[255]',
        'content' => 'required',
        'post_type' => 'required|in_list[setup_alert,pnl_share,educational,analysis,journal]',
        'asset_type' => 'required|in_list[all,equity,options,futures,crypto,forex]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'title' => [
            'required' => 'Post title is required',
            'max_length' => 'Post title cannot exceed 255 characters'
        ],
        'content' => [
            'required' => 'Post content is required'
        ],
        'post_type' => [
            'required' => 'Post type is required',
            'in_list' => 'Invalid post type'
        ],
        'asset_type' => [
            'required' => 'Asset type is required',
            'in_list' => 'Invalid asset type'
        ]
    ];

    /**
     * Get posts with user information and badges
     */
    public function getPostsWithUserInfo($filters = [], $limit = 20, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' p');
        $builder->select('p.*, u.full_name, u.profile, u.email');
        $builder->join('users u', 'u.id = p.user_id');
        $builder->where('p.deleted_at IS NULL');

        // Apply filters
        if (!empty($filters['post_type']) && $filters['post_type'] !== 'all') {
            $builder->where('p.post_type', $filters['post_type']);
        }

        if (!empty($filters['asset_type']) && $filters['asset_type'] !== 'all') {
            $builder->where('p.asset_type', $filters['asset_type']);
        }

        if (!empty($filters['user_id'])) {
            $builder->where('p.user_id', $filters['user_id']);
        }

        // Apply sorting
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'trending':
                    $builder->orderBy('(p.like_count + p.comment_count + p.share_count)', 'DESC');
                    break;
                case 'most_liked':
                    $builder->orderBy('p.like_count', 'DESC');
                    break;
                case 'top_week':
                    $builder->where('p.created_at >=', date('Y-m-d H:i:s', strtotime('-7 days')));
                    $builder->orderBy('(p.like_count + p.comment_count)', 'DESC');
                    break;
                case 'educational':
                    $builder->where('p.post_type', 'educational');
                    $builder->orderBy('p.created_at', 'DESC');
                    break;
                default:
                    $builder->orderBy('p.created_at', 'DESC');
            }
        } else {
            $builder->orderBy('p.created_at', 'DESC');
        }

        $builder->limit($limit, $offset);
        return $builder->get()->getResultArray();
    }

    /**
     * Get user badges for a specific user
     */
    public function getUserBadges($userId)
    {
        $builder = $this->db->table('user_badges');
        $builder->where('user_id', $userId);
        $builder->where('is_active', true);
        return $builder->get()->getResultArray();
    }

    /**
     * Increment like count
     */
    public function incrementLikeCount($postId)
    {
        $builder = $this->db->table($this->table);
        $builder->set('like_count', 'like_count + 1', false);
        $builder->where('id', $postId);
        return $builder->update();
    }

    /**
     * Decrement like count
     */
    public function decrementLikeCount($postId)
    {
        $builder = $this->db->table($this->table);
        $builder->set('like_count', 'like_count - 1', false);
        $builder->where('id', $postId);
        $builder->where('like_count >', 0);
        return $builder->update();
    }

    /**
     * Increment comment count
     */
    public function incrementCommentCount($postId)
    {
        $builder = $this->db->table($this->table);
        $builder->set('comment_count', 'comment_count + 1', false);
        $builder->where('id', $postId);
        return $builder->update();
    }

    /**
     * Decrement comment count
     */
    public function decrementCommentCount($postId)
    {
        $builder = $this->db->table($this->table);
        $builder->set('comment_count', 'comment_count - 1', false);
        $builder->where('id', $postId);
        $builder->where('comment_count >', 0);
        return $builder->update();
    }

    /**
     * Get post by ID with user info
     */
    public function getPostWithUserInfo($postId)
    {
        $builder = $this->db->table($this->table . ' p');
        $builder->select('p.*, u.full_name, u.profile, u.email');
        $builder->join('users u', 'u.id = p.user_id');
        $builder->where('p.id', $postId);
        $builder->where('p.deleted_at IS NULL');
        return $builder->get()->getRowArray();
    }
}
