<?php

namespace App\Models;

use CodeIgniter\Model;

class CommunityFollowModel extends Model
{
    protected $table = 'community_follows';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = null;

    protected $allowedFields = [
        'follower_id',
        'following_id'
    ];

    protected $validationRules = [
        'follower_id' => 'required|integer',
        'following_id' => 'required|integer|differs[follower_id]'
    ];

    protected $validationMessages = [
        'follower_id' => [
            'required' => 'Follower ID is required',
            'integer' => 'Follower ID must be an integer'
        ],
        'following_id' => [
            'required' => 'Following ID is required',
            'integer' => 'Following ID must be an integer',
            'differs' => 'You cannot follow yourself'
        ]
    ];

    /**
     * Check if user is following another user
     */
    public function isFollowing($followerId, $followingId)
    {
        return $this->where('follower_id', $followerId)
                   ->where('following_id', $followingId)
                   ->first() !== null;
    }

    /**
     * Toggle follow relationship
     */
    public function toggleFollow($followerId, $followingId)
    {
        // Prevent self-following
        if ($followerId == $followingId) {
            return false;
        }

        $existingFollow = $this->where('follower_id', $followerId)
                              ->where('following_id', $followingId)
                              ->first();

        if ($existingFollow) {
            // Unfollow - remove the relationship
            $this->delete($existingFollow['id']);
            return false; // Unfollowed
        } else {
            // Follow - add the relationship
            $this->insert([
                'follower_id' => $followerId,
                'following_id' => $followingId
            ]);
            return true; // Followed
        }
    }

    /**
     * Get followers of a user
     */
    public function getFollowers($userId, $limit = 50, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' f');
        $builder->select('u.id, u.full_name, u.profile, u.email, f.created_at as followed_at');
        $builder->join('users u', 'u.id = f.follower_id');
        $builder->where('f.following_id', $userId);
        $builder->orderBy('f.created_at', 'DESC');
        $builder->limit($limit, $offset);
        return $builder->get()->getResultArray();
    }

    /**
     * Get users that a user is following
     */
    public function getFollowing($userId, $limit = 50, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' f');
        $builder->select('u.id, u.full_name, u.profile, u.email, f.created_at as followed_at');
        $builder->join('users u', 'u.id = f.following_id');
        $builder->where('f.follower_id', $userId);
        $builder->orderBy('f.created_at', 'DESC');
        $builder->limit($limit, $offset);
        return $builder->get()->getResultArray();
    }

    /**
     * Get follower count for a user
     */
    public function getFollowerCount($userId)
    {
        return $this->where('following_id', $userId)->countAllResults();
    }

    /**
     * Get following count for a user
     */
    public function getFollowingCount($userId)
    {
        return $this->where('follower_id', $userId)->countAllResults();
    }

    /**
     * Get mutual followers between two users
     */
    public function getMutualFollowers($userId1, $userId2, $limit = 10)
    {
        $builder = $this->db->table($this->table . ' f1');
        $builder->select('u.id, u.full_name, u.profile');
        $builder->join($this->table . ' f2', 'f1.follower_id = f2.follower_id');
        $builder->join('users u', 'u.id = f1.follower_id');
        $builder->where('f1.following_id', $userId1);
        $builder->where('f2.following_id', $userId2);
        $builder->limit($limit);
        return $builder->get()->getResultArray();
    }

    /**
     * Get suggested users to follow (users with most followers that current user isn't following)
     */
    public function getSuggestedUsers($currentUserId, $limit = 10)
    {
        $builder = $this->db->table('users u');
        $builder->select('u.id, u.full_name, u.profile, COUNT(f.follower_id) as follower_count');
        $builder->join($this->table . ' f', 'f.following_id = u.id', 'left');
        $builder->where('u.id !=', $currentUserId);
        $builder->where('u.deleted_at IS NULL');
        
        // Exclude users already being followed
        $subquery = $this->db->table($this->table)
                            ->select('following_id')
                            ->where('follower_id', $currentUserId);
        $builder->whereNotIn('u.id', $subquery);
        
        $builder->groupBy('u.id, u.full_name, u.profile');
        $builder->orderBy('follower_count', 'DESC');
        $builder->limit($limit);
        
        return $builder->get()->getResultArray();
    }

    /**
     * Get posts from users that current user follows
     */
    public function getFollowingPosts($userId, $limit = 20, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' f');
        $builder->select('p.*, u.full_name, u.profile');
        $builder->join('community_posts p', 'p.user_id = f.following_id');
        $builder->join('users u', 'u.id = p.user_id');
        $builder->where('f.follower_id', $userId);
        $builder->where('p.deleted_at IS NULL');
        $builder->orderBy('p.created_at', 'DESC');
        $builder->limit($limit, $offset);
        return $builder->get()->getResultArray();
    }
}
