<?php
echo "=== Community Setup Verification ===\n\n";

$db = new mysqli('localhost', 'root', '', 'diary');
if ($db->connect_error) {
    die('Connection failed: ' . $db->connect_error);
}

// Check if all community tables exist
echo "1. Checking community tables...\n";
$tables = ['community_posts', 'community_likes', 'community_comments', 'community_follows', 'user_badges'];
$allTablesExist = true;

foreach ($tables as $table) {
    $result = $db->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "   ✓ Table '$table' exists\n";
    } else {
        echo "   ✗ Table '$table' does not exist\n";
        $allTablesExist = false;
    }
}

if (!$allTablesExist) {
    echo "\nSome tables are missing!\n";
    exit(1);
}

// Check if sample data exists
echo "\n2. Checking sample data...\n";

$result = $db->query("SELECT COUNT(*) as count FROM community_posts");
$row = $result->fetch_assoc();
echo "   Posts: " . $row['count'] . "\n";

$result = $db->query("SELECT COUNT(*) as count FROM community_likes");
$row = $result->fetch_assoc();
echo "   Likes: " . $row['count'] . "\n";

$result = $db->query("SELECT COUNT(*) as count FROM community_comments");
$row = $result->fetch_assoc();
echo "   Comments: " . $row['count'] . "\n";

$result = $db->query("SELECT COUNT(*) as count FROM user_badges");
$row = $result->fetch_assoc();
echo "   Badges: " . $row['count'] . "\n";

// Check if files exist
echo "\n3. Checking required files...\n";

$files = [
    'app/Controllers/CommunityController.php' => 'Community Controller',
    'app/Models/CommunityPostModel.php' => 'Post Model',
    'app/Models/CommunityLikeModel.php' => 'Like Model',
    'app/Models/CommunityCommentModel.php' => 'Comment Model',
    'app/Models/CommunityFollowModel.php' => 'Follow Model',
    'app/Models/UserBadgeModel.php' => 'Badge Model',
    'app/Views/pages/community.php' => 'Community View',
    'assets/js/community.js' => 'Community JavaScript',
    'assets/css/community.css' => 'Community CSS'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "   ✓ $description exists\n";
    } else {
        echo "   ✗ $description missing\n";
    }
}

// Check routes configuration
echo "\n4. Checking routes...\n";
$routesContent = file_get_contents('app/Config/Routes.php');
if (strpos($routesContent, 'CommunityController') !== false) {
    echo "   ✓ Community routes configured\n";
} else {
    echo "   ✗ Community routes not configured\n";
}

// Show sample posts
echo "\n5. Sample posts in database:\n";
$result = $db->query("SELECT id, title, post_type, like_count, comment_count FROM community_posts ORDER BY created_at DESC LIMIT 3");
while ($row = $result->fetch_assoc()) {
    echo "   - [{$row['post_type']}] {$row['title']} (Likes: {$row['like_count']}, Comments: {$row['comment_count']})\n";
}

$db->close();

echo "\n=== Verification Complete ===\n";
echo "\n✅ Community functionality is ready!\n";
echo "\nNext steps:\n";
echo "1. Start your XAMPP Apache server\n";
echo "2. Navigate to: http://localhost/your-project/Community\n";
echo "3. Test the community features in your browser\n";
echo "\nAPI Endpoints available:\n";
echo "- GET  /community/posts (get posts)\n";
echo "- POST /community/create-post (create post)\n";
echo "- POST /community/toggle-like (like/unlike)\n";
echo "- POST /community/add-comment (add comment)\n";
echo "- GET  /community/comments (get comments)\n";
echo "- POST /community/toggle-follow (follow/unfollow)\n";
echo "- GET  /community/my-posts (user's posts)\n";
?>
