<?php
$db = new mysqli('localhost', 'root', '', 'diary');
if ($db->connect_error) {
    die('Connection failed: ' . $db->connect_error);
}

echo "Fixing migration records...\n";

// Check if migrations table exists
$result = $db->query("SHOW TABLES LIKE 'migrations'");
if ($result->num_rows == 0) {
    echo "Creating migrations table...\n";
    $sql = "CREATE TABLE `migrations` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `version` varchar(255) NOT NULL,
        `class` varchar(255) NOT NULL,
        `group` varchar(255) NOT NULL,
        `namespace` varchar(255) NOT NULL,
        `time` int(11) NOT NULL,
        `batch` int(11) unsigned NOT NULL,
        PRIMARY KEY (`id`)
    )";
    if (!$db->query($sql)) {
        die("Error creating migrations table: " . $db->error);
    }
}

// Insert migration records for existing tables
$migrations = [
    ['2025-01-07-120000', 'App\\Database\\Migrations\\CreateContactMessagesTable', 'default', 'App', time(), 1],
    ['2025-01-09-120000', 'App\\Database\\Migrations\\AddAngelOneSupport', 'default', 'App', time(), 2],
    ['2025-01-11-120000', 'App\\Database\\Migrations\\AddAngelOneTotpKey', 'default', 'App', time(), 3],
    ['2025-01-15-120000', 'App\\Database\\Migrations\\CreateChallengesTable', 'default', 'App', time(), 4],
    ['2025-01-16-120000', 'App\\Database\\Migrations\\CreateRulesSystem', 'default', 'App', time(), 5]
];

foreach ($migrations as $migration) {
    $version = $migration[0];
    $class = $migration[1];
    $group = $migration[2];
    $namespace = $migration[3];
    $time = $migration[4];
    $batch = $migration[5];
    
    // Check if migration record already exists
    $check = $db->prepare("SELECT id FROM migrations WHERE version = ?");
    $check->bind_param("s", $version);
    $check->execute();
    $result = $check->get_result();
    
    if ($result->num_rows == 0) {
        $stmt = $db->prepare("INSERT INTO migrations (version, class, `group`, namespace, time, batch) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssii", $version, $class, $group, $namespace, $time, $batch);
        if ($stmt->execute()) {
            echo "Added migration record for $version\n";
        } else {
            echo "Error adding migration record for $version: " . $db->error . "\n";
        }
        $stmt->close();
    } else {
        echo "Migration record for $version already exists\n";
    }
    $check->close();
}

echo "Migration records fixed!\n";
$db->close();
?>
