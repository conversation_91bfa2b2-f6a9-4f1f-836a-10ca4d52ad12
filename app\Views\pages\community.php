<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trade Diary | Trading Community</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        // Set base URL for JavaScript
        const base_url = '<?= base_url() ?>';
    </script>
    <style>
        :root {
            --primary: #4fd1c5;
            --primary-dark: #319795;
            --secondary: #805ad5;
            --secondary-dark: #6b46c1;
            --accent: #f687b3;
            --accent-dark: #e53e3e;
            --dark: #1a202c;
            --darker: #171923;
            --light: #f7fafc;
            --gray: #e2e8f0;
            --dark-gray: #2d3748;
        }

        body {
            font-family: 'Space Grotesk', sans-serif;
            background-color: var(--darker);
            color: var(--gray);
            transition: all 0.3s ease;
        }

        .glass-card {
            background: rgba(26, 32, 44, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .sidebar {
            transition: all 0.3s ease;
            background-color: var(--dark);
        }

        .sidebar-collapsed {
            width: 80px !important;
        }

        .sidebar-collapsed .nav-text {
            display: none;
        }

        .sidebar-collapsed .logo-text {
            display: none;
        }

        .sidebar-collapsed .logo-icon {
            margin: 0 auto;
        }

        .post-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(79, 209, 197, 0.1);
        }

        .glow-button {
            box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
            }

            50% {
                box-shadow: 0 0 25px rgba(79, 209, 197, 0.8);
            }

            100% {
                box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
            }
        }

        .hashtag {
            color: var(--primary);
        }

        .mention {
            color: var(--accent);
        }

        .stock-tag {
            color: var(--secondary);
        }

        .neon-text {
            text-shadow: 0 0 5px rgba(79, 209, 197, 0.5);
        }

        .modal {
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            display: none;
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
            display: flex;
        }

        .modal-content {
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .modal.active .modal-content {
            transform: translateY(0);
        }

        .post-type-btn.active {
            background-color: var(--primary) !important;
            color: white !important;
        }

        .tab-button.tab-active {
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
        }

        .loading-spinner {
            border: 3px solid rgba(79, 209, 197, 0.3);
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 40;
        }

        .overlay.active {
            display: block;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -100%;
                z-index: 50;
                height: 100vh;
                transition: left 0.3s ease;
            }

            .sidebar.active {
                left: 0;
            }

            .main-content {
                margin-left: 0 !important;
            }

            .filters-container {
                flex-direction: column;
                gap: 1rem;
            }

            .filters-container>div {
                justify-content: space-between;
            }

            .modal-content {
                width: 95%;
                margin: 0 auto;
            }
        }
    </style>
</head>

<body>
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="sidebar w-64 flex-shrink-0 flex flex-col border-r border-gray-800 transition-all duration-300 ease-in-out">
            <div class="flex items-center justify-between p-4 border-b border-gray-800">
                <div class="flex items-center space-x-2">
                    <div class="logo-icon">
                        <i class="fas fa-book text-2xl text-teal-400"></i>
                    </div>
                    <span class="logo-text text-xl font-bold text-teal-400 neon-text">TRADE DIARY</span>
                </div>
                <button id="sidebarToggle" class="text-gray-400 hover:text-white focus:outline-none">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>

            <div class="flex-1 overflow-y-auto py-4">
                <nav>
                    <ul class="space-y-2 px-2">
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-white bg-gray-800">
                                <i class="fas fa-home text-teal-400 w-6"></i>
                                <span class="nav-text ml-3">Home Feed</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-pen text-blue-400 w-6"></i>
                                <span class="nav-text ml-3">My Posts</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-lightbulb text-purple-400 w-6"></i>
                                <span class="nav-text ml-3">Setups & Ideas</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-chart-line text-green-400 w-6"></i>
                                <span class="nav-text ml-3">P&L Shares</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-search-dollar text-yellow-400 w-6"></i>
                                <span class="nav-text ml-3">Market Analysis</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-book-open text-orange-400 w-6"></i>
                                <span class="nav-text ml-3">Trading Journal</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-users text-pink-400 w-6"></i>
                                <span class="nav-text ml-3">Following</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-trophy text-yellow-500 w-6"></i>
                                <span class="nav-text ml-3">Leaderboard</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- User Profile Section -->
            <div class="p-4 border-t border-gray-800">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="nav-text">
                        <p class="text-sm font-medium text-white">John Trader</p>
                        <p class="text-xs text-gray-400">Pro Analyst</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Overlay -->
        <div class="overlay"></div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <div class="glass-card border-b border-gray-800 p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button id="mobileSidebarToggle" class="md:hidden text-gray-400 hover:text-white">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="text-2xl font-bold text-white">Trading Community</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="text-gray-400 hover:text-white">
                            <i class="fas fa-search text-xl"></i>
                        </button>
                        <button class="text-gray-400 hover:text-white">
                            <i class="fas fa-bell text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="flex-1 overflow-y-auto p-6">
                <!-- Filters -->
                <div class="filters-container flex items-center justify-between mb-6 flex-wrap gap-4">
                    <div class="flex items-center space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Asset Type</label>
                            <select name="asset_type" class="bg-gray-800 border border-gray-600 text-white rounded-lg px-3 py-2 focus:border-primary focus:outline-none">
                                <option value="all">All Assets</option>
                                <option value="nifty">Nifty</option>
                                <option value="banknifty">Bank Nifty</option>
                                <option value="stocks">Stocks</option>
                                <option value="forex">Forex</option>
                                <option value="crypto">Crypto</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Sort By</label>
                            <select name="sort" class="bg-gray-800 border border-gray-600 text-white rounded-lg px-3 py-2 focus:border-primary focus:outline-none">
                                <option value="latest">Latest</option>
                                <option value="popular">Most Popular</option>
                                <option value="trending">Trending</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="flex border-b border-gray-800 mb-6 tabs">
                    <button class="tab-button tab-active px-4 py-2 font-medium text-white">All</button>
                    <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Setups</button>
                    <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">P&L</button>
                    <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Analysis</button>
                    <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Journals</button>
                </div>

                <!-- Posts -->
                <div class="space-y-6" id="postsContainer">
                    <!-- Posts will be loaded dynamically via JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="add-post-btn fixed bottom-6 right-6 w-14 h-14 bg-primary hover:bg-primary-dark text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40">
        <i class="fas fa-plus text-xl"></i>
    </button>

    <!-- Add Post Modal -->
    <div class="modal fixed inset-0 z-50 flex items-center justify-center p-4">
        <div class="absolute inset-0 bg-black bg-opacity-70"></div>
        <div class="modal-content glass-card rounded-xl w-full max-w-2xl relative">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-white">Create New Post</h2>
                    <button class="close-modal text-gray-400 hover:text-white text-2xl">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="createPostForm" class="space-y-6">
                    <!-- Post Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-3">Post Type</label>
                        <div class="flex flex-wrap gap-2">
                            <button type="button" class="post-type-btn px-4 py-2 rounded-lg bg-gray-700 text-gray-300 hover:bg-primary hover:text-white transition-colors" data-type="setup_alert">Setup Alert</button>
                            <button type="button" class="post-type-btn px-4 py-2 rounded-lg bg-gray-700 text-gray-300 hover:bg-primary hover:text-white transition-colors" data-type="pnl_share">P&L Share</button>
                            <button type="button" class="post-type-btn px-4 py-2 rounded-lg bg-gray-700 text-gray-300 hover:bg-primary hover:text-white transition-colors" data-type="educational">Educational</button>
                            <button type="button" class="post-type-btn px-4 py-2 rounded-lg bg-gray-700 text-gray-300 hover:bg-primary hover:text-white transition-colors" data-type="analysis">Analysis</button>
                            <button type="button" class="post-type-btn px-4 py-2 rounded-lg bg-gray-700 text-gray-300 hover:bg-primary hover:text-white transition-colors" data-type="journal">Journal</button>
                        </div>
                        <input type="hidden" name="post_type" id="postType" required>
                    </div>

                    <!-- Title -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                        <input type="text" name="title" class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary focus:outline-none" placeholder="Enter post title..." required>
                    </div>

                    <!-- Content -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Content</label>
                        <textarea name="content" rows="6" class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary focus:outline-none resize-none" placeholder="Share your trading insights..." required></textarea>
                    </div>

                    <!-- Chart/Image Upload -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Chart/Image (Optional)</label>
                        <input type="file" name="image" accept="image/*" class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-primary file:text-white hover:file:bg-primary-dark">
                    </div>

                    <!-- Tags -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Tags</label>
                        <input type="text" name="tags" class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary focus:outline-none" placeholder="Add hashtags, mentions, stock symbols... (e.g., #BankNifty @TradeMaster $RELIANCE)">
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-4 pt-4">
                        <button type="button" class="close-modal px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">Cancel</button>
                        <button type="submit" class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors">Post</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Include Community JavaScript -->
    <script src="<?= base_url('assets/js/community.js') ?>"></script>
</body>

</html>
                    <li>
                        <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                            <i class="fas fa-binoculars text-red-400 w-6"></i>
                            <span class="nav-text ml-3">Watchlist</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                            <i class="fas fa-bell text-indigo-400 w-6"></i>
                            <span class="nav-text ml-3">Notifications</span>
                            <span class="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">3</span>
                        </a>
                    </li>
                </ul>

                <div class="border-t border-gray-800 mt-4 pt-4 px-2">
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-user text-cyan-400 w-6"></i>
                                <span class="nav-text ml-3">Profile</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-cog text-gray-400 w-6"></i>
                                <span class="nav-text ml-3">Settings</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="p-4 border-t border-gray-800">
            <div class="flex items-center">
                <img src="<?= $userDetails['profile'] ?? 'https://randomuser.me/api/portraits/men/32.jpg' ?>" alt="User"
                    class="w-10 h-10 rounded-full border-2 border-teal-400">
                <div class="ml-3">
                    <div class="text-sm font-medium text-white"><?= $userDetails['full_name'] ?? 'TraderX' ?></div>
                    <div class="text-xs text-gray-400">Pro Member</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overlay for mobile -->
    <div class="overlay"></div>

    <!-- Main content -->
    <div class="flex-1 overflow-y-auto bg-gray-900">
        <!-- Mobile header -->
        <div class="lg:hidden flex items-center justify-between p-4 border-b border-gray-800">
            <button id="mobileSidebarToggle" class="text-gray-400 hover:text-white focus:outline-none">
                <i class="fas fa-bars text-xl"></i>
            </button>
            <div class="text-xl font-bold text-teal-400 neon-text">TRADE DIARY</div>
            <div class="w-6"></div>
        </div>

        <!-- Main feed -->
        <div class="p-4">
            <!-- Feed header with filters -->
            <div class="flex items-center justify-between mb-6 feed-header">
                <h1 class="text-2xl font-bold text-white neon-text">COMMUNITY FEED</h1>
                <div class="flex space-x-2 feed-filters">
                    <div class="relative">
                        <select name="asset_type"
                            class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                            <option value="all" selected>All Assets</option>
                            <option value="equity">Equity</option>
                            <option value="options">Options</option>
                            <option value="futures">Futures</option>
                            <option value="crypto">Crypto</option>
                            <option value="forex">Forex</option>
                        </select>
                    </div>
                    <div class="relative">
                        <select name="sort"
                            class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                            <option value="latest" selected>Latest</option>
                            <option value="trending">Trending</option>
                            <option value="top_week">Top This Week</option>
                            <option value="most_liked">Most Liked</option>
                            <option value="educational">Educational</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <div class="flex border-b border-gray-800 mb-6 tabs">
                <button class="tab-button tab-active px-4 py-2 font-medium text-white">All</button>
                <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Setups</button>
                <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">P&L</button>
                <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Analysis</button>
                <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Journals</button>
            </div>

            <!-- Posts -->
            <div class="space-y-6" id="postsContainer">
                <!-- Posts will be loaded dynamically via JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Add Post Modal -->
<div class="modal fixed inset-0 z-50 flex items-center justify-center p-4">
    <div class="absolute inset-0 bg-black bg-opacity-70"></div>
    <div class="modal-content glass-card rounded-xl w-full max-w-2xl relative">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-white">Create New Post</h2>
                <button class="text-gray-400 hover:text-white" onclick="toggleModal()">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="postForm">
                <!-- Post Type Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Post Type</label>
                    <div class="flex flex-wrap gap-2 post-type-buttons">
                        <button type="button" class="post-type-btn active px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="setup_alert">Setup Alert</button>
                        <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="pnl_share">P&L Share</button>
                        <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="educational">Educational</button>
                        <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="analysis">Analysis</button>
                        <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="journal">Journal</button>
                    </div>
                </div>

                <!-- Title -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                    <input type="text" id="postTitle" name="title" required
                        class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500"
                        placeholder="Enter post title...">
                </div>

                <!-- Content -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Content</label>
                    <textarea id="postContent" name="content" required rows="4"
                        class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500"
                        placeholder="Share your trading insights..."></textarea>
                </div>

                <!-- Image Upload -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Chart/Image (Optional)</label>
                    <input type="file" id="postImage" name="image" accept="image/*"
                        class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500">
                </div>

                <!-- Tags -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Tags</label>
                    <input type="text" id="postTags" name="tags"
                        class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500"
                        placeholder="Add hashtags, mentions, stock symbols... (e.g., #BankNifty @TradeMaster $NIFTY)">
                    <div id="tagsContainer" class="mt-2 flex flex-wrap gap-2"></div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="toggleModal()"
                        class="px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-800">
                        Cancel
                    </button>
                    <button type="submit"
                        class="px-4 py-2 bg-teal-600 hover:bg-teal-500 text-white rounded-lg glow-button">
                        Post
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<button onclick="toggleModal()" 
    class="fixed bottom-6 right-6 w-14 h-14 bg-teal-600 hover:bg-teal-500 text-white rounded-full shadow-lg glow-button z-40">
    <i class="fas fa-plus text-xl"></i>
</button>

<script>
// Set base URL for JavaScript
const base_url = '<?= base_url() ?>';

// Modal functionality
function toggleModal() {
    const modal = document.querySelector('.modal');
    modal.classList.toggle('active');
}

// Post type selection
document.addEventListener('click', function(e) {
    if (e.target.matches('.post-type-btn')) {
        document.querySelectorAll('.post-type-btn').forEach(btn => btn.classList.remove('active'));
        e.target.classList.add('active');
    }
});
</script>

<!-- Include Community JavaScript -->
<script src="<?= base_url('assets/js/community.js') ?>"></script>
