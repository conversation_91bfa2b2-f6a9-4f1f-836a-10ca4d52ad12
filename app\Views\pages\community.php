<!-- Community Feed Page -->
<link rel="stylesheet" href="<?= base_url('assets/css/community.css') ?>">
<div class="flex h-screen overflow-hidden">
    <!-- Sidebar -->
    <div class="sidebar w-64 flex-shrink-0 flex flex-col border-r border-gray-800 transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between p-4 border-b border-gray-800">
            <div class="flex items-center space-x-2">
                <div class="logo-icon">
                    <i class="fas fa-book text-2xl text-teal-400"></i>
                </div>
                <span class="logo-text text-xl font-bold text-teal-400 neon-text">TRADE DIARY</span>
            </div>
            <button id="sidebarToggle" class="text-gray-400 hover:text-white focus:outline-none">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>

        <div class="flex-1 overflow-y-auto py-4">
            <nav>
                <ul class="space-y-2 px-2">
                    <li>
                        <a href="#" class="flex items-center p-3 rounded-lg text-white bg-gray-800">
                            <i class="fas fa-home text-teal-400 w-6"></i>
                            <span class="nav-text ml-3">Home Feed</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                            <i class="fas fa-pen text-blue-400 w-6"></i>
                            <span class="nav-text ml-3">My Posts</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                            <i class="fas fa-lightbulb text-purple-400 w-6"></i>
                            <span class="nav-text ml-3">Setups & Ideas</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                            <i class="fas fa-chart-line text-green-400 w-6"></i>
                            <span class="nav-text ml-3">P&L Shares</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                            <i class="fas fa-search-dollar text-yellow-400 w-6"></i>
                            <span class="nav-text ml-3">Market Analysis</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                            <i class="fas fa-binoculars text-red-400 w-6"></i>
                            <span class="nav-text ml-3">Watchlist</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                            <i class="fas fa-bell text-indigo-400 w-6"></i>
                            <span class="nav-text ml-3">Notifications</span>
                            <span class="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">3</span>
                        </a>
                    </li>
                </ul>

                <div class="border-t border-gray-800 mt-4 pt-4 px-2">
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-user text-cyan-400 w-6"></i>
                                <span class="nav-text ml-3">Profile</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white">
                                <i class="fas fa-cog text-gray-400 w-6"></i>
                                <span class="nav-text ml-3">Settings</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="p-4 border-t border-gray-800">
            <div class="flex items-center">
                <img src="<?= $userDetails['profile'] ?? 'https://randomuser.me/api/portraits/men/32.jpg' ?>" alt="User"
                    class="w-10 h-10 rounded-full border-2 border-teal-400">
                <div class="ml-3">
                    <div class="text-sm font-medium text-white"><?= $userDetails['full_name'] ?? 'TraderX' ?></div>
                    <div class="text-xs text-gray-400">Pro Member</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overlay for mobile -->
    <div class="overlay"></div>

    <!-- Main content -->
    <div class="flex-1 overflow-y-auto bg-gray-900">
        <!-- Mobile header -->
        <div class="lg:hidden flex items-center justify-between p-4 border-b border-gray-800">
            <button id="mobileSidebarToggle" class="text-gray-400 hover:text-white focus:outline-none">
                <i class="fas fa-bars text-xl"></i>
            </button>
            <div class="text-xl font-bold text-teal-400 neon-text">TRADE DIARY</div>
            <div class="w-6"></div>
        </div>

        <!-- Main feed -->
        <div class="p-4">
            <!-- Feed header with filters -->
            <div class="flex items-center justify-between mb-6 feed-header">
                <h1 class="text-2xl font-bold text-white neon-text">COMMUNITY FEED</h1>
                <!-- Debug button -->
                <button onclick="testConnection()" class="bg-red-600 text-white px-3 py-1 rounded text-sm">Test API</button>
                <div class="flex space-x-2 feed-filters">
                    <div class="relative">
                        <select name="asset_type"
                            class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                            <option value="all" selected>All Assets</option>
                            <option value="equity">Equity</option>
                            <option value="options">Options</option>
                            <option value="futures">Futures</option>
                            <option value="crypto">Crypto</option>
                            <option value="forex">Forex</option>
                        </select>
                    </div>
                    <div class="relative">
                        <select name="sort"
                            class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                            <option value="latest" selected>Latest</option>
                            <option value="trending">Trending</option>
                            <option value="top_week">Top This Week</option>
                            <option value="most_liked">Most Liked</option>
                            <option value="educational">Educational</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <div class="flex border-b border-gray-800 mb-6 tabs">
                <button class="tab-button tab-active px-4 py-2 font-medium text-white">All</button>
                <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Setups</button>
                <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">P&L</button>
                <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Analysis</button>
                <button class="tab-button px-4 py-2 font-medium text-gray-400 hover:text-white">Journals</button>
            </div>

            <!-- Posts -->
            <div class="space-y-6" id="postsContainer">
                <!-- Posts will be loaded dynamically via JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Add Post Modal -->
<div class="modal fixed inset-0 z-50 flex items-center justify-center p-4">
    <div class="absolute inset-0 bg-black bg-opacity-70"></div>
    <div class="modal-content glass-card rounded-xl w-full max-w-2xl relative">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-white">Create New Post</h2>
                <button class="text-gray-400 hover:text-white" onclick="toggleModal()">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="postForm">
                <!-- Post Type Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Post Type</label>
                    <div class="flex flex-wrap gap-2 post-type-buttons">
                        <button type="button" class="post-type-btn active px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="setup_alert">Setup Alert</button>
                        <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="pnl_share">P&L Share</button>
                        <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="educational">Educational</button>
                        <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="analysis">Analysis</button>
                        <button type="button" class="post-type-btn px-4 py-2 rounded-lg border border-gray-600 text-sm"
                            data-type="journal">Journal</button>
                    </div>
                </div>

                <!-- Title -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                    <input type="text" id="postTitle" name="title" required
                        class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500"
                        placeholder="Enter post title...">
                </div>

                <!-- Content -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Content</label>
                    <textarea id="postContent" name="content" required rows="4"
                        class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500"
                        placeholder="Share your trading insights..."></textarea>
                </div>

                <!-- Image Upload -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Chart/Image (Optional)</label>
                    <input type="file" id="postImage" name="image" accept="image/*"
                        class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500">
                </div>

                <!-- Tags -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Tags</label>
                    <input type="text" id="postTags" name="tags"
                        class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-teal-500"
                        placeholder="Add hashtags, mentions, stock symbols... (e.g., #BankNifty @TradeMaster $NIFTY)">
                    <div id="tagsContainer" class="mt-2 flex flex-wrap gap-2"></div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="toggleModal()"
                        class="px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-800">
                        Cancel
                    </button>
                    <button type="submit"
                        class="px-4 py-2 bg-teal-600 hover:bg-teal-500 text-white rounded-lg glow-button">
                        Post
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<button onclick="toggleModal()" 
    class="fixed bottom-6 right-6 w-14 h-14 bg-teal-600 hover:bg-teal-500 text-white rounded-full shadow-lg glow-button z-40">
    <i class="fas fa-plus text-xl"></i>
</button>

<script>
// Set base URL for JavaScript
const base_url = '<?= base_url() ?>';

// Modal functionality
function toggleModal() {
    const modal = document.querySelector('.modal');
    modal.classList.toggle('active');
}

// Post type selection
document.addEventListener('click', function(e) {
    if (e.target.matches('.post-type-btn')) {
        document.querySelectorAll('.post-type-btn').forEach(btn => btn.classList.remove('active'));
        e.target.classList.add('active');
    }
});

// Test function
function testConnection() {
    console.log('Testing API connection...');
    fetch('/community/posts')
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            alert('API Response: ' + JSON.stringify(data));
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error: ' + error.message);
        });
}
</script>

<!-- Include Community JavaScript -->
<script src="<?= base_url('assets/js/community.js') ?>"></script>
