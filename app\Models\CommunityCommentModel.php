<?php

namespace App\Models;

use CodeIgniter\Model;

class CommunityCommentModel extends Model
{
    protected $table = 'community_comments';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;

    protected $useSoftDeletes = true;
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $allowedFields = [
        'user_id',
        'post_id',
        'parent_comment_id',
        'content'
    ];

    protected $validationRules = [
        'user_id' => 'required|integer',
        'post_id' => 'required|integer',
        'content' => 'required|max_length[1000]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'post_id' => [
            'required' => 'Post ID is required',
            'integer' => 'Post ID must be an integer'
        ],
        'content' => [
            'required' => 'Comment content is required',
            'max_length' => 'Comment cannot exceed 1000 characters'
        ]
    ];

    /**
     * Get comments for a post with user information
     */
    public function getPostComments($postId, $limit = 50)
    {
        $builder = $this->db->table($this->table . ' c');
        $builder->select('c.*, u.full_name, u.profile, u.email');
        $builder->join('users u', 'u.id = c.user_id');
        $builder->where('c.post_id', $postId);
        $builder->where('c.deleted_at IS NULL');
        $builder->orderBy('c.created_at', 'ASC');
        $builder->limit($limit);
        
        $comments = $builder->get()->getResultArray();
        
        // Organize comments into threaded structure
        return $this->organizeCommentsThreaded($comments);
    }

    /**
     * Organize comments into threaded structure
     */
    private function organizeCommentsThreaded($comments)
    {
        $threaded = [];
        $commentMap = [];

        // First pass: create a map of all comments
        foreach ($comments as $comment) {
            $comment['replies'] = [];
            $commentMap[$comment['id']] = $comment;
        }

        // Second pass: organize into threaded structure
        foreach ($commentMap as $comment) {
            if ($comment['parent_comment_id'] === null) {
                // Top-level comment
                $threaded[] = $comment;
            } else {
                // Reply to another comment
                if (isset($commentMap[$comment['parent_comment_id']])) {
                    $commentMap[$comment['parent_comment_id']]['replies'][] = $comment;
                }
            }
        }

        return $threaded;
    }

    /**
     * Get comment count for a post
     */
    public function getCommentCount($postId)
    {
        return $this->where('post_id', $postId)
                   ->where('deleted_at IS NULL')
                   ->countAllResults();
    }

    /**
     * Get recent comments by a user
     */
    public function getUserRecentComments($userId, $limit = 10)
    {
        $builder = $this->db->table($this->table . ' c');
        $builder->select('c.*, p.title as post_title, p.id as post_id');
        $builder->join('community_posts p', 'p.id = c.post_id');
        $builder->where('c.user_id', $userId);
        $builder->where('c.deleted_at IS NULL');
        $builder->where('p.deleted_at IS NULL');
        $builder->orderBy('c.created_at', 'DESC');
        $builder->limit($limit);
        return $builder->get()->getResultArray();
    }

    /**
     * Add a new comment
     */
    public function addComment($data)
    {
        if ($this->insert($data)) {
            $commentId = $this->getInsertID();
            
            // Update post comment count
            $postModel = new CommunityPostModel();
            $postModel->incrementCommentCount($data['post_id']);
            
            return $commentId;
        }
        return false;
    }

    /**
     * Delete a comment and update post count
     */
    public function deleteComment($commentId)
    {
        $comment = $this->find($commentId);
        if ($comment) {
            if ($this->delete($commentId)) {
                // Update post comment count
                $postModel = new CommunityPostModel();
                $postModel->decrementCommentCount($comment['post_id']);
                return true;
            }
        }
        return false;
    }

    /**
     * Get comment with user info
     */
    public function getCommentWithUserInfo($commentId)
    {
        $builder = $this->db->table($this->table . ' c');
        $builder->select('c.*, u.full_name, u.profile, u.email');
        $builder->join('users u', 'u.id = c.user_id');
        $builder->where('c.id', $commentId);
        $builder->where('c.deleted_at IS NULL');
        return $builder->get()->getRowArray();
    }

    /**
     * Get replies to a specific comment
     */
    public function getCommentReplies($parentCommentId)
    {
        $builder = $this->db->table($this->table . ' c');
        $builder->select('c.*, u.full_name, u.profile, u.email');
        $builder->join('users u', 'u.id = c.user_id');
        $builder->where('c.parent_comment_id', $parentCommentId);
        $builder->where('c.deleted_at IS NULL');
        $builder->orderBy('c.created_at', 'ASC');
        return $builder->get()->getResultArray();
    }
}
