<?php

namespace App\Models;

use CodeIgniter\Model;

class CommunityLikeModel extends Model
{
    protected $table = 'community_likes';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = null;

    protected $allowedFields = [
        'user_id',
        'post_id'
    ];

    protected $validationRules = [
        'user_id' => 'required|integer',
        'post_id' => 'required|integer'
    ];

    /**
     * Check if user has liked a post
     */
    public function hasUserLikedPost($userId, $postId)
    {
        return $this->where('user_id', $userId)
                   ->where('post_id', $postId)
                   ->first() !== null;
    }

    /**
     * Toggle like for a post
     */
    public function toggleLike($userId, $postId)
    {
        $existingLike = $this->where('user_id', $userId)
                            ->where('post_id', $postId)
                            ->first();

        if ($existingLike) {
            // Unlike - remove the like
            $this->delete($existingLike['id']);
            return false; // Unliked
        } else {
            // Like - add the like
            $this->insert([
                'user_id' => $userId,
                'post_id' => $postId
            ]);
            return true; // Liked
        }
    }

    /**
     * Get like count for a post
     */
    public function getLikeCount($postId)
    {
        return $this->where('post_id', $postId)->countAllResults();
    }

    /**
     * Get users who liked a post
     */
    public function getPostLikers($postId, $limit = 10)
    {
        $builder = $this->db->table($this->table . ' l');
        $builder->select('u.id, u.full_name, u.profile');
        $builder->join('users u', 'u.id = l.user_id');
        $builder->where('l.post_id', $postId);
        $builder->orderBy('l.created_at', 'DESC');
        $builder->limit($limit);
        return $builder->get()->getResultArray();
    }

    /**
     * Get posts liked by a user
     */
    public function getUserLikedPosts($userId, $limit = 20, $offset = 0)
    {
        $builder = $this->db->table($this->table . ' l');
        $builder->select('p.*, u.full_name, u.profile');
        $builder->join('community_posts p', 'p.id = l.post_id');
        $builder->join('users u', 'u.id = p.user_id');
        $builder->where('l.user_id', $userId);
        $builder->where('p.deleted_at IS NULL');
        $builder->orderBy('l.created_at', 'DESC');
        $builder->limit($limit, $offset);
        return $builder->get()->getResultArray();
    }
}
