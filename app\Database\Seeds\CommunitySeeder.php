<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class CommunitySeeder extends Seeder
{
    public function run()
    {
        // First, let's create some sample user badges
        $badgeData = [
            [
                'user_id' => 1,
                'badge_type' => 'pro_analyst',
                'badge_name' => 'Pro Analyst',
                'badge_color' => 'purple',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'user_id' => 1,
                'badge_type' => 'consistent_gainer',
                'badge_name' => 'Consistent Gainer',
                'badge_color' => 'green',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'user_id' => 1,
                'badge_type' => 'chart_master',
                'badge_name' => 'Chart Master',
                'badge_color' => 'blue',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('user_badges')->insertBatch($badgeData);

        // Create sample community posts
        $postData = [
            [
                'user_id' => 1,
                'title' => 'BankNifty Breakout Setup - 15min Chart',
                'content' => 'Identified a bullish flag pattern on BankNifty 15min chart. Breakout above 44,200 with volume confirmation could target 44,600. Stop loss below 44,000.',
                'post_type' => 'setup_alert',
                'asset_type' => 'options',
                'hashtags' => 'BankNifty,BreakoutSetup,Intraday',
                'mentions' => 'TradeMaster',
                'stock_tags' => 'BANKNIFTY',
                'like_count' => 24,
                'comment_count' => 8,
                'share_count' => 3,
                'is_featured' => false,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ],
            [
                'user_id' => 1,
                'title' => '+₹28,500 Today - Nifty Options',
                'content' => 'Executed 3 trades today in Nifty options. All were based on the 5min ORB strategy I shared last week. Key was waiting for confirmation after the first 30min.',
                'post_type' => 'pnl_share',
                'asset_type' => 'options',
                'hashtags' => 'OptionsTrading,ORBStrategy,Nifty',
                'mentions' => '',
                'stock_tags' => 'NIFTY',
                'like_count' => 42,
                'comment_count' => 15,
                'share_count' => 7,
                'is_featured' => true,
                'created_at' => date('Y-m-d H:i:s', strtotime('-5 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-5 hours'))
            ],
            [
                'user_id' => 1,
                'title' => 'Python Backtest: EMA Crossover Strategy',
                'content' => 'Sharing a simple EMA crossover strategy backtest for BankNifty futures. Tested on 5 years data with 55% win rate and 1.8 risk:reward. Code in comments.',
                'post_type' => 'educational',
                'asset_type' => 'futures',
                'hashtags' => 'AlgorithmicTrading,Python,Backtesting',
                'mentions' => 'DataScientist',
                'stock_tags' => 'BANKNIFTY',
                'like_count' => 37,
                'comment_count' => 12,
                'share_count' => 5,
                'is_featured' => false,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
            [
                'user_id' => 1,
                'title' => 'Weekly Market Analysis - Bullish Momentum',
                'content' => 'Markets showing strong bullish momentum this week. Key levels to watch: Nifty 19800 resistance, BankNifty 44500. Sectoral rotation favoring IT and Pharma.',
                'post_type' => 'analysis',
                'asset_type' => 'equity',
                'hashtags' => 'MarketAnalysis,WeeklyOutlook,Nifty',
                'mentions' => '',
                'stock_tags' => 'NIFTY,BANKNIFTY',
                'like_count' => 18,
                'comment_count' => 6,
                'share_count' => 2,
                'is_featured' => false,
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
            ],
            [
                'user_id' => 1,
                'title' => 'Trading Journal: Lessons from Last Month',
                'content' => 'Reflecting on last month\'s trades. Key takeaways: 1) Patience pays off 2) Risk management is crucial 3) Emotional discipline makes the difference. Win rate: 68%',
                'post_type' => 'journal',
                'asset_type' => 'all',
                'hashtags' => 'TradingJournal,Lessons,RiskManagement',
                'mentions' => '',
                'stock_tags' => '',
                'like_count' => 31,
                'comment_count' => 9,
                'share_count' => 4,
                'is_featured' => false,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 week'))
            ]
        ];

        $this->db->table('community_posts')->insertBatch($postData);

        // Create sample likes
        $likeData = [
            [
                'user_id' => 1,
                'post_id' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ],
            [
                'user_id' => 1,
                'post_id' => 2,
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 hours'))
            ]
        ];

        $this->db->table('community_likes')->insertBatch($likeData);

        // Create sample comments
        $commentData = [
            [
                'user_id' => 1,
                'post_id' => 1,
                'parent_comment_id' => null,
                'content' => 'Great setup! I\'ll be watching for the breakout confirmation.',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ],
            [
                'user_id' => 1,
                'post_id' => 1,
                'parent_comment_id' => 1,
                'content' => 'Thanks! Make sure to wait for volume confirmation.',
                'created_at' => date('Y-m-d H:i:s', strtotime('-45 minutes')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-45 minutes'))
            ],
            [
                'user_id' => 1,
                'post_id' => 2,
                'parent_comment_id' => null,
                'content' => 'Impressive results! Can you share more details about your risk management approach?',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ],
            [
                'user_id' => 1,
                'post_id' => 3,
                'parent_comment_id' => null,
                'content' => 'Nice implementation! Have you tried optimizing the EMA periods?',
                'created_at' => date('Y-m-d H:i:s', strtotime('-20 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-20 hours'))
            ]
        ];

        $this->db->table('community_comments')->insertBatch($commentData);

        echo "Community sample data seeded successfully!\n";
    }
}
